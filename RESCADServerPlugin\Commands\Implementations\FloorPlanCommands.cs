using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Diagnostics;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Windows;

namespace RESCADServerPlugin.Commands.Implementations
{
    /// <summary>
    /// 分层平面图生成命令实现类
    /// </summary>
    public class FloorPlanCommands : CommandBase
    {
        /// <summary>
        /// XData中的数据索引常量 - CDCH HI
        /// </summary>
        private const int CDCH_HI_BUILDING_NUMBER_INDEX = 2;  // 1（栋）
        private const int CDCH_HI_FLOOR_NUMBER_INDEX = 3;     // -2（楼层）
        private const int CDCH_HI_UNIT_NUMBER_INDEX = 4;      // （单元）
        private const int CDCH_HI_ROOM_NUMBER_INDEX = 5;      // 5（房号）
        private const int CDCH_HI_NORTH_ARROW_ORIENTATION_INDEX = 23; // 123（指北针方位）

        /// <summary>
        /// XData中的数据索引常量 - CDCHPPI
        /// </summary>
        private const int CDCHPPI_BUILDING_NUMBER_INDEX = 4;  // 25（栋）
        private const int CDCHPPI_FLOOR_NUMBER_INDEX = 5;     // 2（楼层）

        /// <summary>
        /// 支持的XData应用程序名称
        /// </summary>
        private const string XDATA_APP_NAME_CDCH_HI = "CDCH HI";
        private const string XDATA_APP_NAME_CDCHPPI = "CDCHPPI";

        /// <summary>
        /// 标签动态缩放的参考值
        /// </summary>
        private const double REFERENCE_LONG_SIDE_LENGTH = 50850.0;
        private const double REFERENCE_TEXT_HEIGHT = 600.0;
        private const double REFERENCE_UNIT_LABEL_OFFSET = 1300.0;
        private const double REFERENCE_FLOOR_LABEL_OFFSET = 2400.0;

        /// <summary>
        /// 执行生成分层平面图命令
        /// </summary>
        public void ExecuteGenerateFloorPlans()
        {
            Document doc = GetActiveDocument();
            if (doc == null)
            {
                WriteMessage("\n无法获取当前活动文档。");
                return;
            }

            Editor ed = GetEditor();
            Database db = GetDatabase();

            // WriteMessage("\n开始生成分层平面图...");

            try
            {
                // 1. 自动创建输出文件夹
                string outputFolder = GetAutoOutputFolder();
                if (string.IsNullOrEmpty(outputFolder))
                {
                    WriteMessage("\n无法创建输出文件夹。");
                    return;
                }

                // WriteMessage($"\n输出文件夹: {outputFolder}");

                // 2. 扫描所有实体并提取XData
                var buildingFloorGroups = ScanEntitiesAndGroupByBuildingFloor(db);
                if (!buildingFloorGroups.Any())
                {
                    WriteMessage("\n未找到包含有效XData的实体。");
                    WriteMessage("\n提示：请使用 RES_DEBUG_CDCH_HI_ENTITIES 命令检查实体的XData结构。");
                    return;
                }

                // WriteMessage($"\n找到 {buildingFloorGroups.Count} 个建筑-楼层组合。");

                // 3. 为每个建筑-楼层组合生成平面图 (指北针数据已在扫描阶段统一处理)
                int totalGenerated = 0;
                int totalCount = buildingFloorGroups.Count; // 总数量
                int currentIndex = 0; // 当前索引

                // 保存已生成的DWG文件路径，用于后续转换为WMF
                List<string> dwgFilesToConvert = new List<string>();

                foreach (var group in buildingFloorGroups)
                {
                    currentIndex++;
                    try
                    {
                        var result = GenerateFloorPlanForGroup(db, group, outputFolder, currentIndex, totalCount);
                        if (result.success)
                        {
                            totalGenerated++;
                            // 将生成的DWG文件路径添加到列表中，用于后续转换为WMF
                            if (!string.IsNullOrEmpty(result.filePath))
                            {
                                dwgFilesToConvert.Add(result.filePath);
                            }
                            // WriteMessage($"\n已生成: {group.Key} ({currentIndex}/{totalCount})");
                        }
                        else
                        {
                            WriteMessage($"\n生成失败: {group.Key} ({currentIndex}/{totalCount})");
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteMessage($"\n生成 {group.Key} 时发生错误: {ex.Message}");
                    }
                }

                WriteMessage($"\n\n分层平面图生成完成！共生成 {totalGenerated} 个文件。");
                WriteMessage($"\n输出目录: {outputFolder}");

                // 转换DWG文件为WMF格式
                if (dwgFilesToConvert.Count > 0)
                {
                    ConvertDwgToWmf(dwgFilesToConvert, outputFolder, GetEditor());
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n生成分层平面图时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 调试命令：检查所有包含CDCH HI XData的实体
        /// </summary>
        public void ExecuteDebugCDCHHIEntities()
        {
            Document doc = GetActiveDocument();
            if (doc == null)
            {
                WriteMessage("\n无法获取当前活动文档。");
                return;
            }

            Database db = GetDatabase();
            WriteMessage("\n开始调试CDCH HI实体...");

            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                    int totalEntities = 0;
                    int entitiesWithXData = 0;
                    int cdchHiEntities = 0;
                    int cdchppiEntities = 0;
                    int validEntities = 0;

                    foreach (ObjectId objId in btr)
                    {
                        Entity entity = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (entity == null) continue;

                        totalEntities++;

                        // 检查实体的XData
                        using (ResultBuffer rb = entity.XData)
                        {
                            if (rb != null)
                            {
                                entitiesWithXData++;
                                TypedValue[] values = rb.AsArray();
                                if (values != null && values.Length > 0)
                                {
                                    // 查找CDCH HI或CDCHPPI应用程序名称
                                    string foundAppName = null;
                                    for (int i = 0; i < values.Length; i++)
                                    {
                                        if (values[i].TypeCode == 1001)
                                        {
                                            string appName = values[i].Value?.ToString();
                                            if (appName == XDATA_APP_NAME_CDCH_HI || appName == XDATA_APP_NAME_CDCHPPI)
                                            {
                                                foundAppName = appName;
                                                break;
                                            }
                                        }
                                    }

                                    if (foundAppName != null)
                                    {
                                        if (foundAppName == XDATA_APP_NAME_CDCH_HI)
                                        {
                                            cdchHiEntities++;
                                        }
                                        else if (foundAppName == XDATA_APP_NAME_CDCHPPI)
                                        {
                                            cdchppiEntities++;
                                        }

                                        WriteMessage($"\n找到{foundAppName}实体: {entity.GetType().Name} (句柄: {entity.Handle})");
                                        WriteMessage($"  XData长度: {values.Length}");

                                        // 显示前10个值用于调试
                                        for (int i = 0; i < Math.Min(values.Length, 10); i++)
                                        {
                                            WriteMessage($"  [{i}] 类型={values[i].TypeCode}, 值={values[i].Value}");
                                        }

                                        // 尝试提取数据
                                        var entityData = ExtractXDataFromEntity(entity);
                                        if (entityData != null)
                                        {
                                            validEntities++;
                                            WriteMessage($"  ✓ 成功提取: 栋={entityData.BuildingNumber}, 层={entityData.FloorNumber}, 单元={entityData.UnitNumber}, 房号={entityData.RoomNumber}");
                                        }
                                        else
                                        {
                                            WriteMessage($"  ✗ 提取失败");
                                        }
                                        WriteMessage("");
                                    }
                                }
                            }
                        }
                    }

                    WriteMessage($"\n=== 调试统计 ===");
                    WriteMessage($"总实体数: {totalEntities}");
                    WriteMessage($"包含XData的实体数: {entitiesWithXData}");
                    WriteMessage($"CDCH HI实体数: {cdchHiEntities}");
                    WriteMessage($"CDCHPPI实体数: {cdchppiEntities}");
                    WriteMessage($"成功提取数据的实体数: {validEntities}");

                    trans.Commit();
                }
                catch (Exception ex)
                {
                    WriteMessage($"\n调试过程中发生错误: {ex.Message}");
                    trans.Abort();
                }
            }
        }

        /// <summary>
        /// 自动获取并准备输出文件夹
        /// </summary>
        private string GetAutoOutputFolder()
        {
            Document doc = GetActiveDocument();
            if (doc == null)
            {
                WriteMessage("\n错误: 无法获取当前活动文档。");
                return null;
            }

            // 1. 初始化和准备输出目录
            // - 获取输出路径：首先，代码从插件配置中读取 PrintDrawingOutputPath。
            // - 处理路径：
            //     - 如果配置的路径存在，则使用该路径。
            //     - 如果路径不存在，则尝试创建它。
            //     - 如果配置不存在或创建失败，则默认使用插件 DLL 文件所在目录下的 PrintDrawingsOutput 文件夹作为输出目录。
            var settings = RESCADServerPlugin.Configuration.ConfigurationService.GetSettings();
            string outputBaseDir;
            string docName = Path.GetFileNameWithoutExtension(doc.Name);

            if (settings != null && !string.IsNullOrWhiteSpace(settings.PrintDrawingOutputPath))
            {
                if (Directory.Exists(settings.PrintDrawingOutputPath))
                {
                    outputBaseDir = settings.PrintDrawingOutputPath;
                }
                else
                {
                    try
                    {
                        Directory.CreateDirectory(settings.PrintDrawingOutputPath);
                        outputBaseDir = settings.PrintDrawingOutputPath;
                    }
                    catch (Exception ex)
                    {
                        WriteMessage($"\n创建配置的输出目录时出错: {ex.Message}。将使用默认目录。");
                        string dllPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                        string dllDir = Path.GetDirectoryName(dllPath);
                        outputBaseDir = Path.Combine(dllDir, "PrintDrawingsOutput");
                        Directory.CreateDirectory(outputBaseDir); // 确保默认目录存在
                    }
                }
            }
            else
            {
                // 如果配置不存在或为空，则使用默认路径
                string dllPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                string dllDir = Path.GetDirectoryName(dllPath);
                outputBaseDir = Path.Combine(dllDir, "PrintDrawingsOutput");
                Directory.CreateDirectory(outputBaseDir); // 确保默认目录存在
            }

            // - 创建子目录：在确定的输出目录下，会根据当前打开的CAD文件名（例如 MyDrawing.dwg）创建一个同名子目录（MyDrawing），用于存放本次操作生成的所有文件。
            string finalOutputFolder = Path.Combine(outputBaseDir, docName);
            Directory.CreateDirectory(finalOutputFolder); // 确保子目录存在

            // - 清空目录：在保存任何新文件之前，程序会清空这个子目录，删除其中所有已存在的文件，以确保每次执行都是一次全新的输出。
            try
            {
                // 删除所有文件
                foreach (string file in Directory.GetFiles(finalOutputFolder))
                {
                    File.Delete(file);
                }
                // 删除所有子目录
                foreach (string dir in Directory.GetDirectories(finalOutputFolder))
                {
                    Directory.Delete(dir, true);
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n清空输出目录 '{finalOutputFolder}' 时出错: {ex.Message}");
                // 如果清空失败，可能不应该继续，但这里我们只记录消息并返回路径
            }

            return finalOutputFolder;
        }

        /// <summary>
        /// 查找用户定义的操作边界框
        /// 当存在多个符合条件的红色闭合四顶点多段线时，选择面积最大的一个
        /// </summary>
        /// <param name="trans">数据库事务</param>
        /// <param name="btr">块表记录（模型空间）</param>
        /// <returns>找到的边界多段线，如果未找到则返回null</returns>
        private Polyline FindUserDefinedBoundary(Transaction trans, BlockTableRecord btr)
        {
            var candidateBoundaries = new List<(Polyline polyline, double area)>();

            // 遍历模型空间中的所有实体，收集所有符合条件的边界框
            foreach (ObjectId objId in btr)
            {
                // 尝试将实体作为多段线打开
                if (trans.GetObject(objId, OpenMode.ForRead) is Polyline pl)
                {
                    // 检查是否满足条件：红色、闭合、4个顶点
                    if (pl.ColorIndex == 1 && pl.Closed && pl.NumberOfVertices == 4)
                    {
                        try
                        {
                            // 计算多段线的面积
                            double area = pl.Area;
                            candidateBoundaries.Add((pl, area));
                            WriteMessage($"\n找到候选红色边界框，面积: {area:F2}");
                        }
                        catch (Exception ex)
                        {
                            WriteMessage($"\n计算边界框面积时发生错误: {ex.Message}，跳过此边界框");
                            continue;
                        }
                    }
                }
            }

            // 如果没有找到任何符合条件的边界框
            if (candidateBoundaries.Count == 0)
            {
                return null; // 未找到符合条件的边界框
            }

            // 如果只有一个候选边界框
            if (candidateBoundaries.Count == 1)
            {
                WriteMessage("\n找到用户定义的红色边界框。");
                return candidateBoundaries[0].polyline;
            }

            // 如果有多个候选边界框，选择面积最大的一个
            var selectedBoundary = candidateBoundaries.OrderByDescending(b => b.area).First();
            WriteMessage($"\n找到 {candidateBoundaries.Count} 个红色边界框，选择面积最大的一个 (面积: {selectedBoundary.area:F2})");

            return selectedBoundary.polyline;
        }

        /// <summary>
        /// 扫描所有实体，按建筑-楼层分组，并统一处理指北针数据。
        /// 此方法通过一次遍历完成实体分组和指北针数据收集，然后通过第二次遍历（内存中的数据）应用指北针，以提高效率。
        /// </summary>
        private Dictionary<string, List<EntityData>> ScanEntitiesAndGroupByBuildingFloor(Database db)
        {
            var groups = new Dictionary<string, List<EntityData>>();
            var buildingNorthArrows = new Dictionary<string, double>();
            Editor ed = GetEditor();

            // 步骤 1: 查找用户标记的边界框并选择内部实体
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                    // 查找用户定义的红色四顶点多段线作为边界
                    Polyline boundary = FindUserDefinedBoundary(trans, btr);
                    if (boundary == null)
                    {
                        WriteMessage("\n未找到用户标记的红色四边形边界框。请绘制一个红色、闭合、有四个顶点的多段线来标记要处理的区域。");
                        return groups; // 返回空字典
                    }

                    // 使用边界框的顶点进行“窗交”选择
                    Point3dCollection polygonVertices = new Point3dCollection();
                    for (int i = 0; i < boundary.NumberOfVertices; i++)
                    {
                        polygonVertices.Add(boundary.GetPoint3dAt(i));
                    }
                    PromptSelectionResult selRes = ed.SelectCrossingPolygon(polygonVertices);

                    if (selRes.Status != PromptStatus.OK || selRes.Value.Count == 0)
                    {
                        WriteMessage("\n在标记的边界框内未找到任何实体。");
                        return groups; // 返回空字典
                    }

                    // 步骤 2: 遍历选中的实体，进行分组并收集指北针数据
                    foreach (SelectedObject selObj in selRes.Value)
                    {
                        if (selObj == null) continue;

                        Entity entity = trans.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (entity == null) continue;

                        // 从实体中提取结构化数据
                        var entityData = ExtractXDataFromEntity(entity);
                        if (entityData != null)
                        {
                            // 按“栋号”和“楼层”进行分组
                            string groupKey = $"{entityData.BuildingNumber}栋{entityData.FloorNumber}层平面图";
                            if (!groups.ContainsKey(groupKey))
                            {
                                groups[groupKey] = new List<EntityData>();
                            }
                            groups[groupKey].Add(entityData);

                            // 收集指北针数据：只从有效的CDCH HI实体中获取，并为每栋楼保存一次
                            if (!string.IsNullOrEmpty(entityData.BuildingNumber) &&
                                entityData.XDataAppName == XDATA_APP_NAME_CDCH_HI &&
                                entityData.NorthArrowOrientation != 0)
                            {
                                if (!buildingNorthArrows.ContainsKey(entityData.BuildingNumber))
                                {
                                    buildingNorthArrows.Add(entityData.BuildingNumber, entityData.NorthArrowOrientation);
                                }
                            }
                        }
                    }
                    trans.Commit();
                }
                catch (Exception ex)
                {
                    WriteMessage($"\n扫描实体时发生错误: {ex.Message}");
                    trans.Abort();
                    return new Dictionary<string, List<EntityData>>(); // 出错时返回空字典
                }
            }

            // 步骤 3: 将收集到的指北针数据应用到所有分组的实体中
            // WriteMessage("\n开始应用北向箭头继承逻辑...");
            foreach (var group in groups)
            {
                // 获取当前组的栋号
                var buildingNumber = group.Value.FirstOrDefault()?.BuildingNumber;

                // 如果该栋有保存的指北针数据，则应用它
                if (!string.IsNullOrEmpty(buildingNumber) && buildingNorthArrows.TryGetValue(buildingNumber, out double inheritedNorthArrow))
                {
                    int updatedCount = 0;
                    foreach (var entity in group.Value)
                    {
                        // 统一设置指北针数据
                        if (entity.NorthArrowOrientation != inheritedNorthArrow)
                        {
                            entity.NorthArrowOrientation = inheritedNorthArrow;
                            updatedCount++;
                        }
                    }
                    if (updatedCount > 0)
                    {
                        // WriteMessage($"\n  已将栋 '{buildingNumber}' 的指北针 {inheritedNorthArrow} 应用到组 '{group.Key}' 中的 {updatedCount} 个实体");
                    }
                }
            }
            // WriteMessage("\n北向箭头数据应用完成。");

            return groups;
        }

        /// <summary>
        /// 从实体中提取XData信息
        /// </summary>
        private EntityData ExtractXDataFromEntity(Entity entity)
        {
            try
            {
                using (ResultBuffer rb = entity.XData)
                {
                    if (rb == null) return null;

                    TypedValue[] values = rb.AsArray();
                    if (values == null || values.Length == 0) return null;

                    // 查找支持的应用程序的XData
                    // 在AutoCAD XData中，第一个TypeCode 1001的条目通常是应用程序名称
                    string foundAppName = null;
                    int appNameIndex = -1;

                    // 搜索所有TypeCode 1001的条目，找到我们支持的应用程序名称
                    for (int i = 0; i < values.Length; i++)
                    {
                        if (values[i].TypeCode == 1001)
                        {
                            string appName = values[i].Value?.ToString();
                            if (appName == XDATA_APP_NAME_CDCH_HI || appName == XDATA_APP_NAME_CDCHPPI)
                            {
                                foundAppName = appName;
                                appNameIndex = i;
                                break;
                            }
                        }
                    }

                    if (foundAppName == null) return null;

                    // 调试：显示XData的完整内容
                    // WriteMessage($"\n找到实体 {entity.GetType().Name} (句柄: {entity.Handle}):");
                    // WriteMessage($"\n  应用程序: {foundAppName} (位于索引 {appNameIndex})");
                    // WriteMessage($"\n  XData包含 {values.Length} 个值:");
                    // for (int i = 0; i < Math.Min(values.Length, 30); i++) // 只显示前30个值
                    // {
                    //     WriteMessage($"\n    [{i}] 类型={values[i].TypeCode}, 值={values[i].Value}");
                    // }

                    // 验证XData结构的完整性
                    // WriteMessage($"\n  开始提取数据，应用程序类型: {foundAppName}");

                    // 提取数据
                    var entityData = new EntityData
                    {
                        EntityId = entity.ObjectId,
                        EntityType = entity.GetType().Name,
                        XDataAppName = foundAppName
                    };

                    // 根据应用程序类型和索引提取数据
                    if (foundAppName == XDATA_APP_NAME_CDCH_HI)
                    {
                        // WriteMessage($"\n  处理 CDCH HI 数据结构，数组长度: {values.Length}");

                        // CDCH HI 数据结构 - 使用更灵活的提取方法
                        entityData.BuildingNumber = ExtractValueAtIndex(values, CDCH_HI_BUILDING_NUMBER_INDEX, "栋号 (CDCH HI)");
                        entityData.FloorNumber = ExtractValueAtIndex(values, CDCH_HI_FLOOR_NUMBER_INDEX, "楼层 (CDCH HI)");
                        entityData.UnitNumber = ExtractValueAtIndex(values, CDCH_HI_UNIT_NUMBER_INDEX, "单元 (CDCH HI)");
                        entityData.RoomNumber = ExtractValueAtIndex(values, CDCH_HI_ROOM_NUMBER_INDEX, "房号 (CDCH HI)");

                        // 提取指北针方位
                        if (values.Length > CDCH_HI_NORTH_ARROW_ORIENTATION_INDEX)
                        {
                            if (double.TryParse(values[CDCH_HI_NORTH_ARROW_ORIENTATION_INDEX].Value?.ToString(), out double orientation))
                            {
                                entityData.NorthArrowOrientation = orientation;
                                // WriteMessage($"\n  指北针方位 (CDCH HI) [索引{CDCH_HI_NORTH_ARROW_ORIENTATION_INDEX}]: {orientation}");
                            }
                            else
                            {
                                // WriteMessage($"\n  指北针方位 (CDCH HI) [索引{CDCH_HI_NORTH_ARROW_ORIENTATION_INDEX}]: 无法解析为数字，使用默认值0");
                            }
                        }
                        else
                        {
                            // WriteMessage($"\n  指北针方位 (CDCH HI): 索引{CDCH_HI_NORTH_ARROW_ORIENTATION_INDEX}超出范围，使用默认值0");
                        }
                    }
                    else if (foundAppName == XDATA_APP_NAME_CDCHPPI)
                    {
                        // WriteMessage($"\n  处理 CDCHPPI 数据结构，数组长度: {values.Length}");

                        // 详细调试：显示关键索引的值
                        // WriteMessage($"\n  CDCHPPI 调试信息:");
                        // WriteMessage($"\n    索引2值: '{(values.Length > 2 ? values[2].Value?.ToString() : "N/A")}'");
                        // WriteMessage($"\n    索引3值: '{(values.Length > 3 ? values[3].Value?.ToString() : "N/A")}'");
                        // WriteMessage($"\n    索引4值: '{(values.Length > 4 ? values[4].Value?.ToString() : "N/A")}'");
                        // WriteMessage($"\n    索引5值: '{(values.Length > 5 ? values[5].Value?.ToString() : "N/A")}'");
                        // WriteMessage($"\n    预期栋号索引: {CDCHPPI_BUILDING_NUMBER_INDEX}");
                        // WriteMessage($"\n    预期楼层索引: {CDCHPPI_FLOOR_NUMBER_INDEX}");

                        // CDCHPPI 数据结构 - 仅使用预定义的常量索引
                        entityData.BuildingNumber = ExtractValueAtIndex(values, CDCHPPI_BUILDING_NUMBER_INDEX, "栋号 (CDCHPPI)");
                        entityData.FloorNumber = ExtractValueAtIndex(values, CDCHPPI_FLOOR_NUMBER_INDEX, "楼层 (CDCHPPI)");

                        // CDCHPPI 实体通常没有单元和房号信息，设置为空
                        entityData.UnitNumber = "";
                        entityData.RoomNumber = "";
                        // WriteMessage($"\n  单元 (CDCHPPI): 无");
                        // WriteMessage($"\n  房号 (CDCHPPI): 无");

                        // CDCHPPI 实体的指北针方位将在后续的继承逻辑中设置
                        entityData.NorthArrowOrientation = 0; // 初始值，将被继承逻辑覆盖
                        // WriteMessage($"\n  指北针方位 (CDCHPPI): 待继承");
                    }
                    else
                    {
                        WriteMessage($"\n  错误: 未知的应用程序名称: {foundAppName}");
                        return null;
                    }

                    // 严格验证必要字段 - 只有在预定义索引位置有有效数据的实体才会被处理
                    if (string.IsNullOrEmpty(entityData.BuildingNumber) || string.IsNullOrEmpty(entityData.FloorNumber))
                    {
                        // WriteMessage($"\n  验证失败: 栋号='{entityData.BuildingNumber}', 楼层='{entityData.FloorNumber}' - 在预定义索引位置缺少必要信息，跳过此实体");
                        return null;
                    }

                    // WriteMessage($"\n  验证成功: 栋号='{entityData.BuildingNumber}', 楼层='{entityData.FloorNumber}', 应用程序='{entityData.XDataAppName}'");
                    return entityData;
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n提取实体XData时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从XData数组中提取指定索引的值
        /// </summary>
        private string ExtractValueAtIndex(TypedValue[] values, int index, string fieldName)
        {
            if (values.Length > index)
            {
                var rawValue = values[index].Value;
                string value = rawValue?.ToString()?.Trim() ?? "";
                // WriteMessage($"\n  {fieldName} [索引{index}]: 原始值='{rawValue}', 处理后='{value}' {(string.IsNullOrEmpty(value) ? "(空值)" : $"(长度:{value.Length})")}");

                // 特别针对单元字段添加额外调试
                // if (fieldName.Contains("单元"))
                // {
                //     WriteMessage($"\n    [单元调试] 原始值类型: {rawValue?.GetType()}, 是否为null: {rawValue == null}, ToString结果: '{rawValue?.ToString()}'");
                //     WriteMessage($"\n    [单元调试] Trim前: '{rawValue?.ToString()}', Trim后: '{value}', 最终是否为空: {string.IsNullOrEmpty(value)}");
                // }

                return value;
            }
            else
            {
                // WriteMessage($"\n  {fieldName} [索引{index}]: 索引超出范围 (数组长度{values.Length})");
                return "";
            }
        }

        // ========================= v v v 代码修改区域 v v v =========================

        /// <summary>
        /// 为指定的建筑-楼层组合生成平面图 (已修改实体拷贝逻辑)
        /// </summary>
        private (bool success, string filePath) GenerateFloorPlanForGroup(Database db, KeyValuePair<string, List<EntityData>> group, string outputFolder, int currentNumber, int totalNumber)
        {
            Database newDb = null;
            try
            {
                // 获取编辑器以执行选择操作
                Editor ed = GetEditor();
                // 创建新的数据库用于存储生成的平面图
                newDb = CreateChineseCompatibleDatabase();

                // 使用源数据库的事务来选择要复制的实体
                using (Transaction sourceTrans = db.TransactionManager.StartTransaction())
                {
                    // 步骤 1: 计算本组“种子”实体（即带有XData的实体）的几何边界
                    Extents3d? seedBounds = CalculateInitialGroupBounds(sourceTrans, group.Value);

                    if (!seedBounds.HasValue)
                    {
                        // WriteMessage($"\n警告: 组 {group.Key} 没有可用于确定边界的实体，跳过。");
                        sourceTrans.Abort();
                        return (false, "");
                    }

                    // 步骤 2: 根据边界创建一个带边距的选择多边形（矩形）
                    double margin = 1000.0; // 边距（单位：绘图单位），确保捕捉到所有相关的几何图形
                    Point3d minPt = new Point3d(seedBounds.Value.MinPoint.X - margin, seedBounds.Value.MinPoint.Y - margin, 0);
                    Point3d maxPt = new Point3d(seedBounds.Value.MaxPoint.X + margin, seedBounds.Value.MaxPoint.Y + margin, 0);

                    var selectionPolygon = new Point3dCollection(new[]
                    {
                        new Point3d(minPt.X, minPt.Y, 0),
                        new Point3d(maxPt.X, minPt.Y, 0),
                        new Point3d(maxPt.X, maxPt.Y, 0),
                        new Point3d(minPt.X, maxPt.Y, 0)
                    });

                    // 步骤 3: 使用“窗交”选择器获取该区域内的所有实体
                    // 这将选择边界内、与边界相交或接触的所有实体
                    PromptSelectionResult selectionResult = ed.SelectCrossingPolygon(selectionPolygon);

                    if (selectionResult.Status != PromptStatus.OK)
                    {
                        // WriteMessage($"\n在组 {group.Key} 的区域内未选择到任何实体，跳过。");
                        sourceTrans.Abort();
                        return (false, "");
                    }

                    // 步骤 4: 使用Wblock方法将所有选中的实体复制到新数据库
                    var idsToClone = new ObjectIdCollection(selectionResult.Value.GetObjectIds());
                    db.Wblock(newDb, idsToClone, Point3d.Origin, DuplicateRecordCloning.Replace);

                    sourceTrans.Commit();
                }

                // === 后续处理部分在新数据库上操作，保持不变 ===
                using (Transaction newTrans = newDb.TransactionManager.StartTransaction())
                {
                    BlockTable newBt = newTrans.GetObject(newDb.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord newBtr = newTrans.GetObject(newBt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    CopyDrawingSettings(db, newDb);
                    ProcessBlockReferences(newDb, newTrans);

                    // 重新扫描新数据库，获取包含有效 ObjectId 的新实体列表
                    var entitiesInNewDb = ReScanEntitiesInNewDb(newDb, newTrans);
                    if (!entitiesInNewDb.Any())
                    {
                        // WriteMessage($"\n在生成的文件 {group.Key} 中未找到任何有效实体，跳过。");
                        newTrans.Abort();
                        return (false, "");
                    }

                    // 步骤 1: 先计算一次初始边界，用于定位标签
                    Extents3d? initialBounds = CalculateBounds(newDb, newTrans);
                    if (!initialBounds.HasValue)
                    {
                        // WriteMessage($"\n无法计算 {group.Key} 的初始边界框，跳过。");
                        newTrans.Abort();
                        return (false, "");
                    }

                    // 步骤 2: 基于初始边界生成标签，标签会被放置在图形下方
                    GenerateLabels(newDb, newTrans, entitiesInNewDb, initialBounds.Value);

                    // 步骤 3: 重新计算总边界，这次会包含新生成的标签
                    Extents3d? finalBounds = CalculateBounds(newDb, newTrans);
                    if (!finalBounds.HasValue)
                    {
                        // WriteMessage($"\n无法计算 {group.Key} 的最终边界框，跳过。");
                        newTrans.Abort();
                        return (false, "");
                    }

                    // 步骤 4: 使用包含所有元素的最终边界来添加外框模板，并获取旋转状态
                    var templateResult = AddFrameTemplate(newDb, newTrans, finalBounds.Value, group.Value.FirstOrDefault()?.NorthArrowOrientation ?? 0);

                    // 更新图号
                    UpdateDrawingNumber(newDb, newTrans, currentNumber, totalNumber);

                    // 新增：设置所有文本样式为宋体
                    SetAllTextStyleToSimSun(newDb, newTrans);

                    // 如果模板被旋转了，则在保存前对所有实体执行反向旋转
                    if (templateResult.wasRotated)
                    {
                        WriteMessage($"\n模板已根据图形边界旋转{templateResult.rotationAngle * 180 / Math.PI:F0}度，正在对 {group.Key} 的所有实体执行反向旋转...");

                        // 重新计算包含所有内容的最终边界，以找到正确的旋转中心
                        Extents3d? boundsForRotation = CalculateBounds(newDb, newTrans);
                        if (boundsForRotation.HasValue)
                        {
                            Point3d center = new Point3d(
                                (boundsForRotation.Value.MinPoint.X + boundsForRotation.Value.MaxPoint.X) / 2,
                                (boundsForRotation.Value.MinPoint.Y + boundsForRotation.Value.MaxPoint.Y) / 2,
                                0
                            );

                            // 创建反向旋转矩阵
                            Matrix3d rotationMatrix = Matrix3d.Rotation(-templateResult.rotationAngle, Vector3d.ZAxis, center);

                            // 遍历并旋转模型空间中的所有实体
                            foreach (ObjectId objId in newBtr)
                            {
                                Entity entity = newTrans.GetObject(objId, OpenMode.ForWrite) as Entity;
                                if (entity != null)
                                {
                                    entity.TransformBy(rotationMatrix);
                                }
                            }

                            WriteMessage($"\n反向旋转完成，所有实体已旋转{-templateResult.rotationAngle * 180 / Math.PI:F0}度");
                        }
                    }

                    newTrans.Commit();
                }

                // 保存文件
                string fileName = $"{group.Key}.dwg";
                string filePath = Path.Combine(outputFolder, fileName);
                newDb.SaveAs(filePath, DwgVersion.Current);

                return (true, filePath);
            }
            catch (Exception ex)
            {
                WriteMessage($"\n生成 {group.Key} 时发生错误: {ex.Message}");
                return (false, "");
            }
            finally
            {
                if (newDb != null)
                {
                    newDb.Dispose();
                }
            }
        }

        /// <summary>
        /// (新增辅助方法) 计算“种子”实体列表的初始边界框
        /// </summary>
        private Extents3d? CalculateInitialGroupBounds(Transaction trans, List<EntityData> seedEntities)
        {
            if (seedEntities == null || !seedEntities.Any())
            {
                return null;
            }

            Extents3d? totalBounds = null;

            foreach (var entityData in seedEntities)
            {
                try
                {
                    Entity entity = trans.GetObject(entityData.EntityId, OpenMode.ForRead) as Entity;
                    if (entity == null) continue;

                    Extents3d entityBounds = entity.GeometricExtents;

                    if (!totalBounds.HasValue)
                    {
                        totalBounds = entityBounds;
                    }
                    else
                    {
                        // 正确地扩展 Extents3d?
                        totalBounds = new Extents3d(
                            new Point3d(
                                Math.Min(totalBounds.Value.MinPoint.X, entityBounds.MinPoint.X),
                                Math.Min(totalBounds.Value.MinPoint.Y, entityBounds.MinPoint.Y),
                                Math.Min(totalBounds.Value.MinPoint.Z, entityBounds.MinPoint.Z)
                            ),
                            new Point3d(
                                Math.Max(totalBounds.Value.MaxPoint.X, entityBounds.MaxPoint.X),
                                Math.Max(totalBounds.Value.MaxPoint.Y, entityBounds.MaxPoint.Y),
                                Math.Max(totalBounds.Value.MaxPoint.Z, entityBounds.MaxPoint.Z)
                            )
                        );
                    }
                }
                catch
                {
                    // 忽略可能没有几何范围或导致错误的实体
                    continue;
                }
            }
            return totalBounds;
        }

        // ========================= ^ ^ ^ 代码修改区域 ^ ^ ^ =========================

        /// <summary>
        /// 计算实体的边界框
        /// </summary>
        private Extents3d? CalculateBounds(Database db, Transaction trans)
        {
            try
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                Extents3d? totalBounds = null;

                foreach (ObjectId objId in btr)
                {
                    Entity entity = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                    if (entity == null) continue;

                    try
                    {
                        Extents3d entityBounds = entity.GeometricExtents;
                        if (!totalBounds.HasValue)
                        {
                            totalBounds = entityBounds;
                        }
                        else
                        {
                            totalBounds = new Extents3d(
                                new Point3d(
                                    Math.Min(totalBounds.Value.MinPoint.X, entityBounds.MinPoint.X),
                                    Math.Min(totalBounds.Value.MinPoint.Y, entityBounds.MinPoint.Y),
                                    Math.Min(totalBounds.Value.MinPoint.Z, entityBounds.MinPoint.Z)
                                ),
                                new Point3d(
                                    Math.Max(totalBounds.Value.MaxPoint.X, entityBounds.MaxPoint.X),
                                    Math.Max(totalBounds.Value.MaxPoint.Y, entityBounds.MaxPoint.Y),
                                    Math.Max(totalBounds.Value.MaxPoint.Z, entityBounds.MaxPoint.Z)
                                )
                            );
                        }
                    }
                    catch
                    {
                        // 某些实体可能没有几何范围，跳过
                        continue;
                    }
                }

                return totalBounds;
            }
            catch (Exception ex)
            {
                WriteMessage($"\n计算边界框时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 生成单元标签和楼层标签（修正了文本居中和动态缩放问题）
        /// </summary>
        private void GenerateLabels(Database db, Transaction trans, List<EntityData> entities, Extents3d bounds)
        {
            try
            {
                // 获取模型空间用于添加新实体
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                // --- 动态尺寸计算 ---
                // 1. 计算楼层边界的长边
                double boundsWidth = bounds.MaxPoint.X - bounds.MinPoint.X;
                double boundsHeight = bounds.MaxPoint.Y - bounds.MinPoint.Y;
                double currentLongSide = Math.Max(boundsWidth, boundsHeight);

                // 2. 计算缩放比例
                double scaleRatio = currentLongSide / REFERENCE_LONG_SIDE_LENGTH;

                // 3. 根据比例动态计算字高和间距
                double dynamicTextHeight = REFERENCE_TEXT_HEIGHT * scaleRatio;
                double dynamicUnitLabelOffset = REFERENCE_UNIT_LABEL_OFFSET * scaleRatio;
                double dynamicFloorLabelOffset = REFERENCE_FLOOR_LABEL_OFFSET * scaleRatio;

                // WriteMessage($"\n楼层边界长边: {currentLongSide:F2}, 参考长边: {REFERENCE_LONG_SIDE_LENGTH}, 缩放比例: {scaleRatio:F2}");
                // WriteMessage($"\n动态字高: {dynamicTextHeight:F2}, 单元Y偏移: {dynamicUnitLabelOffset:F2}, 楼层Y偏移: {dynamicFloorLabelOffset:F2}");


                // --- 单元文本创建逻辑 ---

                // 1. & 2. 筛选 "CDCH HI" 实体并按有效的单元号分组
                var unitGroups = entities
                    .Where(e => e.XDataAppName == XDATA_APP_NAME_CDCH_HI && !string.IsNullOrEmpty(e.UnitNumber?.Trim()))
                    .GroupBy(e => e.UnitNumber.Trim())
                    .ToDictionary(g => g.Key, g => g.ToList());

                // WriteMessage($"\n找到 {unitGroups.Count} 个有效单元组。");

                // 3. 遍历每个单元组，为其创建文本
                foreach (var unitGroup in unitGroups)
                {
                    // 获取当前单元组的所有实体
                    List<EntityData> entitiesInGroup = unitGroup.Value;

                    // 计算该单元组的聚合边界
                    var currentUnitBounds = CalculateUnitBounds(db, trans, entitiesInGroup);

                    if (currentUnitBounds.HasValue)
                    {
                        // 4. 计算文本位置
                        // 水平位置: 使用当前单元聚合边界的中心
                        double labelCenterX = (currentUnitBounds.Value.MinPoint.X + currentUnitBounds.Value.MaxPoint.X) / 2;

                        // 垂直位置: 使用整个楼层边界的底部，并向下偏移动态计算出的值
                        double labelY = bounds.MinPoint.Y - dynamicUnitLabelOffset;

                        Point3d labelPosition = new Point3d(labelCenterX, labelY, 0);

                        // 创建单元文本 (使用 MText 实现更可靠的居中)
                        MText unitLabel = new MText
                        {
                            Contents = $"{unitGroup.Key}单元",
                            Location = labelPosition,
                            TextHeight = dynamicTextHeight, // 使用动态字高
                            Attachment = AttachmentPoint.BottomCenter // 设置附着点为底部中心，实现视觉居中
                        };

                        // 将文本添加到模型空间
                        btr.AppendEntity(unitLabel);
                        trans.AddNewlyCreatedDBObject(unitLabel, true);

                        // WriteMessage($"\n已为“{unitGroup.Key}单元”在位置 ({labelPosition.X:F2}, {labelPosition.Y:F2}) 创建 MText 标签。");
                    }
                }

                // --- 楼层文本创建逻辑 ---
                if (entities.Any())
                {
                    var firstEntity = entities.First();
                    Point3d floorLabelPosition = new Point3d(
                        (bounds.MinPoint.X + bounds.MaxPoint.X) / 2,
                        bounds.MinPoint.Y - dynamicFloorLabelOffset, // 楼层标签在单元标签更下方，使用动态偏移
                        0
                    );

                    // 创建楼层文本 (使用 MText 实现更可靠的居中)
                    MText floorLabel = new MText
                    {
                        Contents = $"{firstEntity.BuildingNumber}栋{firstEntity.FloorNumber}层平面图",
                        Location = floorLabelPosition,
                        TextHeight = dynamicTextHeight, // 使用动态字高
                        Attachment = AttachmentPoint.BottomCenter // 设置附着点为底部中心，实现视觉居中
                    };

                    btr.AppendEntity(floorLabel);
                    trans.AddNewlyCreatedDBObject(floorLabel, true);
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n生成标签时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算单元边界 - 正确实现：基于传入的实体列表计算实际几何边界
        /// </summary>
        private Extents3d? CalculateUnitBounds(Database db, Transaction trans, List<EntityData> unitEntities)
        {
            try
            {
                // WriteMessage($"\n  [DEBUG] 开始计算单元边界，实体数量: {unitEntities.Count}");

                if (unitEntities.Count == 0)
                {
                    // WriteMessage($"\n  [DEBUG] 单元实体列表为空，返回null");
                    return null;
                }

                Extents3d? totalBounds = null;

                // 遍历传入的、属于本单元的实体列表
                foreach (var entityData in unitEntities)
                {
                    try
                    {
                        // 从 ObjectId 获取实体对象
                        Entity entity = trans.GetObject(entityData.EntityId, OpenMode.ForRead) as Entity;
                        if (entity == null) continue;

                        // 获取单个实体的几何边界
                        Extents3d entityBounds = entity.GeometricExtents;

                        // 合并边界
                        if (!totalBounds.HasValue)
                        {
                            totalBounds = entityBounds;
                        }
                        else
                        {
                            // 正确的边界合并：比较最小值和最大值，重新创建边界
                            totalBounds = new Extents3d(
                                new Point3d(
                                    Math.Min(totalBounds.Value.MinPoint.X, entityBounds.MinPoint.X),
                                    Math.Min(totalBounds.Value.MinPoint.Y, entityBounds.MinPoint.Y),
                                    Math.Min(totalBounds.Value.MinPoint.Z, entityBounds.MinPoint.Z)
                                ),
                                new Point3d(
                                    Math.Max(totalBounds.Value.MaxPoint.X, entityBounds.MaxPoint.X),
                                    Math.Max(totalBounds.Value.MaxPoint.Y, entityBounds.MaxPoint.Y),
                                    Math.Max(totalBounds.Value.MaxPoint.Z, entityBounds.MaxPoint.Z)
                                )
                            );
                        }
                    }
                    catch (Exception ex)
                    {
                        // 某些实体可能没有几何范围，或者在事务中获取失败，记录并跳过
                        // WriteMessage($"\n  [WARN] 计算实体 {entityData.EntityId} 边界时出错: {ex.Message}");
                        continue;
                    }
                }

                if (totalBounds.HasValue)
                {
                    // WriteMessage($"\n  [DEBUG] 单元边界计算完成: Min({totalBounds.Value.MinPoint.X:F2}, {totalBounds.Value.MinPoint.Y:F2}), Max({totalBounds.Value.MaxPoint.X:F2}, {totalBounds.Value.MaxPoint.Y:F2})");
                }
                else
                {
                    // WriteMessage($"\n  [DEBUG] 未能计算出有效的单元边界，将使用默认边界。");
                    return GetDefaultBounds(); // 如果所有实体都没有有效边界，则返回默认值
                }

                return totalBounds;
            }
            catch (Exception ex)
            {
                WriteMessage($"\n计算单元边界时发生严重错误: {ex.Message}");
                return GetDefaultBounds(); // 发生未知错误时返回默认值
            }
        }

        /// <summary>
        /// 计算楼层的整体边界
        /// </summary>
        private Extents3d? CalculateFloorBounds(Database db, Transaction trans)
        {
            try
            {
                // 获取模型空间
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                if (bt == null) return null;

                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;
                if (btr == null) return null;

                Extents3d? totalBounds = null;
                int entityCount = 0;

                // 遍历所有实体计算总边界
                foreach (ObjectId objId in btr)
                {
                    try
                    {
                        Entity entity = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (entity == null) continue;

                        Extents3d entityBounds = entity.GeometricExtents;

                        if (!totalBounds.HasValue)
                        {
                            totalBounds = entityBounds;
                        }
                        else
                        {
                            totalBounds = new Extents3d(
                                new Point3d(
                                    Math.Min(totalBounds.Value.MinPoint.X, entityBounds.MinPoint.X),
                                    Math.Min(totalBounds.Value.MinPoint.Y, entityBounds.MinPoint.Y),
                                    Math.Min(totalBounds.Value.MinPoint.Z, entityBounds.MinPoint.Z)
                                ),
                                new Point3d(
                                    Math.Max(totalBounds.Value.MaxPoint.X, entityBounds.MaxPoint.X),
                                    Math.Max(totalBounds.Value.MaxPoint.Y, entityBounds.MaxPoint.Y),
                                    Math.Max(totalBounds.Value.MaxPoint.Z, entityBounds.MaxPoint.Z)
                                )
                            );
                        }
                        entityCount++;
                    }
                    catch
                    {
                        continue;
                    }
                }

                // WriteMessage($"\n  [DEBUG] 楼层边界计算完成，处理了 {entityCount} 个实体");
                return totalBounds;
            }
            catch (Exception ex)
            {
                WriteMessage($"\n计算楼层边界时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取默认边界作为后备方案
        /// </summary>
        private Extents3d GetDefaultBounds()
        {
            double defaultWidth = 10000;
            double defaultHeight = 8000;
            Point3d centerPoint = new Point3d(0, 0, 0);

            return new Extents3d(
                new Point3d(centerPoint.X - defaultWidth / 2, centerPoint.Y - defaultHeight / 2, 0),
                new Point3d(centerPoint.X + defaultWidth / 2, centerPoint.Y + defaultHeight / 2, 0)
            );
        }

        /// <summary>
        /// 添加外框模板
        /// </summary>
        private (bool wasRotated, double rotationAngle) AddFrameTemplate(Database db, Transaction trans, Extents3d bounds, double northArrowOrientation)
        {
            try
            {
                // WriteMessage("\n开始添加外框模板...");

                // 获取模板文件路径
                string templatePath = GetTemplatePath();
                // WriteMessage($"\n模板文件路径: {templatePath}");

                if (string.IsNullOrEmpty(templatePath) || !File.Exists(templatePath))
                {
                    // WriteMessage($"\n模板文件不存在，使用备用边界框: {templatePath}");
                    // 如果模板文件不存在，创建简单的红色边界框作为备用
                    CreateSimpleBoundaryFrame(db, trans, bounds);
                    return (false, 0);
                }

                // 读取模板文件
                using (Database templateDb = new Database(false, true))
                {
                    // WriteMessage("\n正在读取模板文件...");
                    templateDb.ReadDwgFile(templatePath, FileOpenMode.OpenForReadAndAllShare, true, "");
                    // WriteMessage("\n模板文件读取成功");

                    // 从模板中获取红色多线段和其他实体
                    var templateData = ExtractTemplateData(templateDb);
                    if (templateData.RedPolyline == null)
                    {
                        // WriteMessage("\n模板文件中未找到红色边界多线段，使用备用边界框");
                        CreateSimpleBoundaryFrame(db, trans, bounds);
                        return (false, 0);
                    }

                    // WriteMessage($"\n模板数据提取成功: 红色边界={templateData.RedPolyline != null}, 指北针实体={templateData.NorthArrowEntities.Count}, 其他实体={templateData.OtherEntities.Count}");

                    // 计算缩放和变换矩阵，并获取旋转状态
                    var transformResult = CalculateTemplateTransform(templateData.RedPolyline, bounds);
                    Matrix3d transformMatrix = transformResult.transformMatrix;
                    bool wasRotated = transformResult.wasRotated;
                    double rotationAngle = wasRotated ? Math.PI / 2 : 0;
                    // WriteMessage("\n变换矩阵计算完成");

                    // 复制模板实体到目标数据库并应用变换
                    CopyTemplateEntities(templateDb, db, trans, transformMatrix, northArrowOrientation, templateData);
                    // WriteMessage("\n外框模板添加完成");

                    return (wasRotated, rotationAngle);
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n添加外框模板时发生错误: {ex.Message}");
                // WriteMessage($"\n错误堆栈: {ex.StackTrace}");
                // 发生错误时创建简单边界框作为备用
                try
                {
                    // WriteMessage("\n尝试创建备用边界框...");
                    CreateSimpleBoundaryFrame(db, trans, bounds);
                    // WriteMessage("\n备用边界框创建成功");
                }
                catch (Exception fallbackEx)
                {
                    WriteMessage($"\n创建备用边界框时发生错误: {fallbackEx.Message}");
                }
                return (false, 0);
            }
        }

        /// <summary>
        /// 更新图号
        /// </summary>
        private void UpdateDrawingNumber(Database db, Transaction trans, int currentNumber, int totalNumber)
        {
            try
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                // 查找包含"N/T"的文字并更新
                foreach (ObjectId objId in btr)
                {
                    Entity entity = trans.GetObject(objId, OpenMode.ForWrite) as Entity;
                    if (entity is DBText dbText && dbText.TextString.Contains("N/T"))
                    {
                        dbText.TextString = dbText.TextString.Replace("N/T", $"{currentNumber}/{totalNumber}");
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n更新图号时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算模板变换矩阵
        /// </summary>
        private (Matrix3d transformMatrix, bool wasRotated) CalculateTemplateTransform(Polyline templatePolyline, Extents3d targetBounds)
        {
            try
            {
                // 获取模板多线段的边界
                Extents3d templateBounds = templatePolyline.GeometricExtents;

                // 定义合理的边距（改为较小的值）
                double margin = 1000;

                // 计算实体内容的中心点和尺寸
                Point3d entitiesCenter = new Point3d(
                    (targetBounds.MinPoint.X + targetBounds.MaxPoint.X) / 2,
                    (targetBounds.MinPoint.Y + targetBounds.MaxPoint.Y) / 2,
                    0
                );
                double entitiesWidth = targetBounds.MaxPoint.X - targetBounds.MinPoint.X;
                double entitiesHeight = targetBounds.MaxPoint.Y - targetBounds.MinPoint.Y;

                // 计算模板的中心点和尺寸
                Point3d templateCenter = new Point3d(
                    (templateBounds.MinPoint.X + templateBounds.MaxPoint.X) / 2,
                    (templateBounds.MinPoint.Y + templateBounds.MaxPoint.Y) / 2,
                    0
                );
                double templateWidth = templateBounds.MaxPoint.X - templateBounds.MinPoint.X;
                double templateHeight = templateBounds.MaxPoint.Y - templateBounds.MinPoint.Y;

                // 判断实体和模板的方向（横向/纵向）
                bool isEntitiesLandscape = entitiesWidth > entitiesHeight;
                bool isTemplateLandscape = templateWidth > templateHeight;

                double rotationAngle = 0;
                bool wasRotated = false;
                // 如果方向不匹配，则旋转模板90度
                if (isEntitiesLandscape != isTemplateLandscape)
                {
                    rotationAngle = Math.PI / 2;
                    wasRotated = true;
                    double temp = templateWidth;
                    templateWidth = templateHeight;
                    templateHeight = temp;
                }

                // 计算考虑边距后的目标尺寸
                double targetWidth = entitiesWidth + 2 * margin;
                double targetHeight = entitiesHeight + 2 * margin;

                // 计算缩放比例，确保模板能完全包围实体内容
                double scaleX = targetWidth / templateWidth;
                double scaleY = targetHeight / templateHeight;
                // 使用较大的缩放比例，确保两个方向都能容纳内容
                double scale = Math.Max(scaleX, scaleY) * 1.1; // 额外增加10%的缩放以确保有足够空间

                // 创建变换矩阵
                Matrix3d transformMatrix = Matrix3d.Identity;

                // 1. 首先将模板移动到原点
                transformMatrix = Matrix3d.Displacement(Point3d.Origin - templateCenter) * transformMatrix;

                // 2. 应用旋转（如果需要）
                if (rotationAngle != 0)
                {
                    transformMatrix = Matrix3d.Rotation(rotationAngle, Vector3d.ZAxis, Point3d.Origin) * transformMatrix;
                }

                // 3. 应用缩放
                transformMatrix = Matrix3d.Scaling(scale, Point3d.Origin) * transformMatrix;

                // 4. 最后移动到目标位置
                transformMatrix = Matrix3d.Displacement(entitiesCenter - Point3d.Origin) * transformMatrix;

                // WriteMessage($"\n变换矩阵计算完成 - 缩放: {scale}, 旋转: {rotationAngle * 180 / Math.PI}度");
                // WriteMessage($"\n目标中心点: ({entitiesCenter.X}, {entitiesCenter.Y})");

                return (transformMatrix, wasRotated);
            }
            catch (Exception ex)
            {
                WriteMessage($"\n计算变换矩阵时发生错误: {ex.Message}");
                return (Matrix3d.Identity, false);
            }
        }

        /// <summary>
        /// 复制模板实体到目标数据库
        /// </summary>
        private void CopyTemplateEntities(Database templateDb, Database targetDb, Transaction targetTrans,
            Matrix3d transformMatrix, double northArrowOrientation, TemplateData templateData)
        {
            try
            {
                // WriteMessage("\n开始复制模板实体...");

                BlockTable bt = targetTrans.GetObject(targetDb.BlockTableId, OpenMode.ForRead) as BlockTable;
                if (bt == null)
                {
                    WriteMessage("\n无法获取目标数据库的块表");
                    return;
                }

                BlockTableRecord btr = targetTrans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                if (btr == null)
                {
                    WriteMessage("\n无法获取目标数据库的模型空间");
                    return;
                }

                // 复制红色边界多线段
                if (templateData.RedPolyline != null)
                {
                    try
                    {
                        Polyline clonedFrame = templateData.RedPolyline.Clone() as Polyline;
                        if (clonedFrame != null)
                        {
                            clonedFrame.TransformBy(transformMatrix);
                            btr.AppendEntity(clonedFrame);
                            targetTrans.AddNewlyCreatedDBObject(clonedFrame, true);
                            // WriteMessage("\n红色边界多线段复制成功");
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteMessage($"\n复制红色边界多线段时发生错误: {ex.Message}");
                    }
                }

                // 复制其他实体
                int otherEntitiesCount = 0;
                foreach (Entity entity in templateData.OtherEntities)
                {
                    try
                    {
                        Entity clonedEntity = entity.Clone() as Entity;
                        if (clonedEntity != null)
                        {
                            clonedEntity.TransformBy(transformMatrix);
                            btr.AppendEntity(clonedEntity);
                            targetTrans.AddNewlyCreatedDBObject(clonedEntity, true);
                            otherEntitiesCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteMessage($"\n复制其他实体时发生错误: {ex.Message}");
                    }
                }
                // WriteMessage($"\n其他实体复制完成，共复制 {otherEntitiesCount} 个实体");

                // 复制并旋转指北针实体
                if (templateData.NorthArrowEntities.Any())
                {
                    try
                    {
                        // WriteMessage($"\n开始处理指北针实体，共 {templateData.NorthArrowEntities.Count} 个");

                        // 1. 先使用主变换矩阵复制指北针实体，以确定其在图中的新位置
                        var transformedArrowEntities = new List<Entity>();
                        foreach (var entity in templateData.NorthArrowEntities)
                        {
                            try
                            {
                                var clonedEntity = entity.Clone() as Entity;
                                if (clonedEntity != null)
                                {
                                    clonedEntity.TransformBy(transformMatrix);
                                    transformedArrowEntities.Add(clonedEntity);
                                }
                            }
                            catch (Exception ex)
                            {
                                WriteMessage($"\n复制指北针实体时发生错误: {ex.Message}");
                            }
                        }

                        if (transformedArrowEntities.Any())
                        {
                            // 2. 计算变换后指北针的中心点，作为二次旋转的基点
                            Point3d transformedArrowCenter = CalculateNorthArrowCenter(transformedArrowEntities, Matrix3d.Identity);

                            // 3. 计算最终需要的旋转角度
                            // 假设模板中的指北针朝上（90度）
                            double initialArrowAngleRad = Math.PI / 2.0;
                            // 从主变换矩阵中提取模板自身的旋转角度
                            CoordinateSystem3d cs = transformMatrix.CoordinateSystem3d;
                            double templateRotationRad = Math.Atan2(cs.Xaxis.Y, cs.Xaxis.X);
                            // 指北针当前的角度 = 初始角度 + 模板旋转角度
                            double currentArrowAngleRad = initialArrowAngleRad + templateRotationRad;
                            // 期望的最终绝对角度
                            double desiredArrowAngleRad = northArrowOrientation * (Math.PI / 180.0);
                            // 需要补充的旋转量
                            double deltaRotationRad = desiredArrowAngleRad - currentArrowAngleRad;

                            // 4. 创建二次旋转矩阵
                            Matrix3d extraRotationMatrix = Matrix3d.Rotation(deltaRotationRad, Vector3d.ZAxis, transformedArrowCenter);

                            // 5. 对已变换的指北针实体应用二次旋转，并添加到数据库
                            int arrowEntitiesAdded = 0;
                            foreach (var transformedEntity in transformedArrowEntities)
                            {
                                try
                                {
                                    transformedEntity.TransformBy(extraRotationMatrix);
                                    btr.AppendEntity(transformedEntity);
                                    targetTrans.AddNewlyCreatedDBObject(transformedEntity, true);
                                    arrowEntitiesAdded++;
                                }
                                catch (Exception ex)
                                {
                                    WriteMessage($"\n添加指北针实体到数据库时发生错误: {ex.Message}");
                                }
                            }
                            // WriteMessage($"\n指北针实体处理完成，共添加 {arrowEntitiesAdded} 个实体");
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteMessage($"\n处理指北针实体时发生错误: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n复制模板实体时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算指北针中心点
        /// </summary>
        private Point3d CalculateNorthArrowCenter(List<Entity> northArrowEntities, Matrix3d transformMatrix)
        {
            try
            {
                if (!northArrowEntities.Any())
                    return Point3d.Origin;

                // 计算所有指北针实体的边界框中心
                Extents3d? totalBounds = null;

                foreach (Entity entity in northArrowEntities)
                {
                    try
                    {
                        Extents3d entityBounds = entity.GeometricExtents;
                        if (!totalBounds.HasValue)
                        {
                            totalBounds = entityBounds;
                        }
                        else
                        {
                            totalBounds = new Extents3d(
                                new Point3d(
                                    Math.Min(totalBounds.Value.MinPoint.X, entityBounds.MinPoint.X),
                                    Math.Min(totalBounds.Value.MinPoint.Y, entityBounds.MinPoint.Y),
                                    Math.Min(totalBounds.Value.MinPoint.Z, entityBounds.MinPoint.Z)
                                ),
                                new Point3d(
                                    Math.Max(totalBounds.Value.MaxPoint.X, entityBounds.MaxPoint.X),
                                    Math.Max(totalBounds.Value.MaxPoint.Y, entityBounds.MaxPoint.Y),
                                    Math.Max(totalBounds.Value.MaxPoint.Z, entityBounds.MaxPoint.Z)
                                )
                            );
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }

                if (totalBounds.HasValue)
                {
                    Point3d center = new Point3d(
                        (totalBounds.Value.MinPoint.X + totalBounds.Value.MaxPoint.X) / 2,
                        (totalBounds.Value.MinPoint.Y + totalBounds.Value.MaxPoint.Y) / 2,
                        0
                    );
                    return center.TransformBy(transformMatrix);
                }

                return Point3d.Origin;
            }
            catch (Exception ex)
            {
                WriteMessage($"\n计算指北针中心点时发生错误: {ex.Message}");
                return Point3d.Origin;
            }
        }

        /// <summary>
        /// 创建简单的边界框作为备用
        /// </summary>
        private void CreateSimpleBoundaryFrame(Database db, Transaction trans, Extents3d bounds)
        {
            try
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                // 计算外框尺寸（比内容稍大）
                double margin = 5000; // 边距
                Point3d minPoint = new Point3d(bounds.MinPoint.X - margin, bounds.MinPoint.Y - margin, 0);
                Point3d maxPoint = new Point3d(bounds.MaxPoint.X + margin, bounds.MaxPoint.Y + margin, 0);

                // 创建红色外框
                Polyline frame = new Polyline();
                frame.AddVertexAt(0, new Point2d(minPoint.X, minPoint.Y), 0, 0, 0);
                frame.AddVertexAt(1, new Point2d(maxPoint.X, minPoint.Y), 0, 0, 0);
                frame.AddVertexAt(2, new Point2d(maxPoint.X, maxPoint.Y), 0, 0, 0);
                frame.AddVertexAt(3, new Point2d(minPoint.X, maxPoint.Y), 0, 0, 0);
                frame.Closed = true;
                frame.ColorIndex = 1; // 红色

                btr.AppendEntity(frame);
                trans.AddNewlyCreatedDBObject(frame, true);
            }
            catch (Exception ex)
            {
                WriteMessage($"\n创建简单边界框时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制绘图设置（文字样式、图层等）
        /// </summary>
        private void CopyDrawingSettings(Database sourceDb, Database targetDb)
        {
            try
            {
                IdMapping idMap = new IdMapping(); // Share IdMapping for all cloning

                using (Transaction sourceTr = sourceDb.TransactionManager.StartTransaction())
                {
                    // 复制文字样式
                    try
                    {
                        ObjectIdCollection textStyleIds = new ObjectIdCollection();
                        TextStyleTable sourceTextStyleTable = sourceTr.GetObject(sourceDb.TextStyleTableId, OpenMode.ForRead) as TextStyleTable;

                        foreach (ObjectId styleId in sourceTextStyleTable)
                        {
                            // 检查对象是否有效且未被删除
                            if (!styleId.IsNull && !styleId.IsErased && styleId.IsValid)
                            {
                                try
                                {
                                    // 尝试打开对象以验证其有效性
                                    var styleObj = sourceTr.GetObject(styleId, OpenMode.ForRead);
                                    if (styleObj != null && !styleObj.IsErased)
                                    {
                                        textStyleIds.Add(styleId);
                                    }
                                }
                                catch
                                {
                                    // 如果无法打开对象，跳过它
                                    continue;
                                }
                            }
                        }

                        if (textStyleIds.Count > 0)
                        {
                            sourceDb.WblockCloneObjects(textStyleIds, targetDb.TextStyleTableId, idMap, DuplicateRecordCloning.Replace, false);
                        }
                    }
                    catch (Exception textStyleEx)
                    {
                        // WriteMessage($"\n复制文字样式时发生错误: {textStyleEx.Message}");
                    }

                    // 复制图层
                    try
                    {
                        ObjectIdCollection layerIds = new ObjectIdCollection();
                        LayerTable sourceLayerTable = sourceTr.GetObject(sourceDb.LayerTableId, OpenMode.ForRead) as LayerTable;

                        foreach (ObjectId layerId in sourceLayerTable)
                        {
                            // 检查对象是否有效且未被删除
                            if (!layerId.IsNull && !layerId.IsErased && layerId.IsValid)
                            {
                                try
                                {
                                    // 尝试打开对象以验证其有效性
                                    var layerObj = sourceTr.GetObject(layerId, OpenMode.ForRead);
                                    if (layerObj != null && !layerObj.IsErased)
                                    {
                                        layerIds.Add(layerId);
                                    }
                                }
                                catch
                                {
                                    // 如果无法打开对象，跳过它
                                    continue;
                                }
                            }
                        }

                        if (layerIds.Count > 0)
                        {
                            sourceDb.WblockCloneObjects(layerIds, targetDb.LayerTableId, idMap, DuplicateRecordCloning.Replace, false);
                        }
                    }
                    catch (Exception layerEx)
                    {
                        // WriteMessage($"\n复制图层时发生错误: {layerEx.Message}");
                    }

                    sourceTr.Commit();
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n复制绘图设置时发生错误: {ex.Message}");
            }
        }



        /// <summary>
        /// 处理块参照的特殊设置
        /// </summary>
        private void ProcessBlockReferences(Database db, Transaction trans)
        {
            try
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                foreach (ObjectId objId in btr)
                {
                    Entity entity = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                    if (entity is BlockReference blockRef)
                    {
                        // 处理块参照的属性
                        AttributeCollection attColl = blockRef.AttributeCollection;
                        if (attColl.Count > 0)
                        {
                            // 遍历所有属性，确保它们正确显示
                            foreach (ObjectId attId in attColl)
                            {
                                AttributeReference attRef = trans.GetObject(attId, OpenMode.ForWrite) as AttributeReference;
                                if (attRef != null)
                                {
                                    // 确保属性可见
                                    attRef.Visible = true;

                                    // 如果属性文字为空或无效，尝试从块定义获取默认值
                                    if (string.IsNullOrEmpty(attRef.TextString))
                                    {
                                        try
                                        {
                                            BlockTableRecord blockDef = trans.GetObject(blockRef.BlockTableRecord, OpenMode.ForRead) as BlockTableRecord;
                                            foreach (ObjectId defObjId in blockDef)
                                            {
                                                Entity defEntity = trans.GetObject(defObjId, OpenMode.ForRead) as Entity;
                                                if (defEntity is AttributeDefinition attDef && attDef.Tag == attRef.Tag)
                                                {
                                                    if (!string.IsNullOrEmpty(attDef.TextString))
                                                    {
                                                        attRef.UpgradeOpen();
                                                        attRef.TextString = attDef.TextString;
                                                    }
                                                    break;
                                                }
                                            }
                                        }
                                        catch
                                        {
                                            // 忽略错误，继续处理其他属性
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n处理块参照时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取模板文件路径
        /// </summary>
        private string GetTemplatePath()
        {
            try
            {
                // 获取当前程序集所在目录
                string assemblyPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                string assemblyDir = Path.GetDirectoryName(assemblyPath);

                // 构建模板文件路径
                string templatePath = Path.Combine(assemblyDir, "Template", "Printeddiagram.dwg");

                return templatePath;
            }
            catch (Exception ex)
            {
                WriteMessage($"\n获取模板路径时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从模板数据库中提取模板数据
        /// </summary>
        private TemplateData ExtractTemplateData(Database templateDb)
        {
            var templateData = new TemplateData();

            using (Transaction trans = templateDb.TransactionManager.StartTransaction())
            {
                try
                {
                    BlockTable bt = trans.GetObject(templateDb.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                    foreach (ObjectId objId in btr)
                    {
                        Entity entity = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (entity == null) continue;

                        // 查找红色闭合多线段作为边界
                        if (entity is Polyline polyline && polyline.Closed && polyline.ColorIndex == 1)
                        {
                            templateData.RedPolyline = polyline.Clone() as Polyline;
                        }
                        // 查找黄色实体作为指北针
                        else if (entity.ColorIndex == 2) // 黄色
                        {
                            templateData.NorthArrowEntities.Add(entity.Clone() as Entity);
                        }
                        // 其他实体
                        else
                        {
                            templateData.OtherEntities.Add(entity.Clone() as Entity);
                        }
                    }

                    trans.Commit();
                }
                catch (Exception ex)
                {
                    WriteMessage($"\n提取模板数据时发生错误: {ex.Message}");
                    trans.Abort();
                }
            }

            return templateData;
        }

        /// <summary>
        /// 将所有文本和块参照属性的文字样式设置为宋体
        /// </summary>
        private void SetAllTextStyleToSimSun(Database db, Transaction tr)
        {
            try
            {
                // 首先确保宋体文字样式存在
                string simSunStyleName = "宋体";
                TextStyleTable textStyleTable = tr.GetObject(db.TextStyleTableId, OpenMode.ForRead) as TextStyleTable;

                ObjectId simSunStyleId;
                if (!textStyleTable.Has(simSunStyleName))
                {
                    // 如果宋体样式不存在，创建它
                    textStyleTable.UpgradeOpen();
                    TextStyleTableRecord simSunStyle = new TextStyleTableRecord();
                    simSunStyle.Name = simSunStyleName;
                    simSunStyle.FileName = "simsun.ttf"; // 宋体字体文件
                    simSunStyle.Font = new Autodesk.AutoCAD.GraphicsInterface.FontDescriptor("宋体", false, false, 0, 0);
                    textStyleTable.Add(simSunStyle);
                    tr.AddNewlyCreatedDBObject(simSunStyle, true);
                    simSunStyleId = simSunStyle.ObjectId;
                }
                else
                {
                    simSunStyleId = textStyleTable[simSunStyleName];
                }

                // 遍历模型空间中的所有实体
                BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                foreach (ObjectId id in btr)
                {
                    // 检查ObjectId是否有效且属于当前数据库
                    if (id.IsNull || id.IsErased || id.Database != db) continue;

                    Entity ent = tr.GetObject(id, OpenMode.ForRead) as Entity;
                    if (ent == null) continue;

                    try
                    {
                        ent.UpgradeOpen();

                        // 处理文本对象
                        if (ent is DBText text)
                        {
                            text.TextStyleId = simSunStyleId;
                        }
                        else if (ent is MText mtext)
                        {
                            mtext.TextStyleId = simSunStyleId;
                        }
                        // 处理块参照
                        else if (ent is BlockReference blockRef)
                        {
                            // 检查块参照是否有属性
                            AttributeCollection attColl = blockRef.AttributeCollection;
                            if (attColl != null && attColl.Count > 0)
                            {
                                // 遍历所有属性
                                foreach (ObjectId attId in attColl)
                                {
                                    if (attId.IsErased || attId.IsNull || attId.Database != db) continue;

                                    try
                                    {
                                        AttributeReference attRef = tr.GetObject(attId, OpenMode.ForWrite) as AttributeReference;
                                        if (attRef != null)
                                        {
                                            attRef.TextStyleId = simSunStyleId;
                                        }
                                    }
                                    catch (Exception attEx)
                                    {
                                        // WriteMessage($"\n设置属性文字样式时出错: {attEx.Message}");
                                    }
                                }
                            }
                        }
                        // 处理所有类型的标注
                        else if (ent is Dimension dim)
                        {
                            dim.TextStyleId = simSunStyleId;
                        }
                    }
                    catch (Exception entEx)
                    {
                        // WriteMessage($"\n设置实体文字样式时出错: {entEx.Message}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                WriteMessage($"\n设置文字样式为宋体时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建中文兼容的数据库
        /// </summary>
        private Database CreateChineseCompatibleDatabase()
        {
            try
            {
                // 尝试使用中文模板文件
                string chineseTemplatePath = GetChineseTemplatePath();
                if (!string.IsNullOrEmpty(chineseTemplatePath) && File.Exists(chineseTemplatePath))
                {
                    // WriteMessage($"\n使用中文模板创建数据库: {chineseTemplatePath}");
                    Database templateDb = new Database(false, true);
                    templateDb.ReadDwgFile(chineseTemplatePath, FileOpenMode.OpenForReadAndAllShare, true, "");

                    // 创建基于模板的新数据库
                    Database newDb = templateDb.Wblock();
                    templateDb.Dispose();

                    // 清空模型空间内容，但保留设置
                    using (Transaction trans = newDb.TransactionManager.StartTransaction())
                    {
                        BlockTable bt = trans.GetObject(newDb.BlockTableId, OpenMode.ForRead) as BlockTable;
                        if (bt != null)
                        {
                            BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                            if (btr != null)
                            {
                                // 清空模型空间中的所有实体
                                List<ObjectId> idsToErase = new List<ObjectId>();
                                foreach (ObjectId objId in btr)
                                {
                                    idsToErase.Add(objId);
                                }

                                foreach (ObjectId objId in idsToErase)
                                {
                                    Entity entity = trans.GetObject(objId, OpenMode.ForWrite) as Entity;
                                    entity?.Erase();
                                }
                            }
                        }
                        trans.Commit();
                    }

                    return newDb;
                }
                else
                {
                    // WriteMessage("\n中文模板文件不存在，使用默认数据库并设置中文支持");
                    // 如果没有中文模板，创建默认数据库并设置中文支持
                    Database newDb = new Database(true, false);
                    SetupChineseSupport(newDb);
                    return newDb;
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n创建中文兼容数据库时发生错误: {ex.Message}");
                // 回退到默认数据库
                Database newDb = new Database(true, false);
                SetupChineseSupport(newDb);
                return newDb;
            }
        }

        /// <summary>
        /// 获取中文模板文件路径
        /// </summary>
        private string GetChineseTemplatePath()
        {
            try
            {
                // 获取当前程序集所在目录
                string assemblyPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                string assemblyDir = Path.GetDirectoryName(assemblyPath);

                // 尝试多个可能的中文模板路径
                string[] possiblePaths = {
                    Path.Combine(assemblyDir, "Template", "acadiso_chinese.dwt"),
                    Path.Combine(assemblyDir, "Template", "acad_chinese.dwt"),
                    Path.Combine(assemblyDir, "Template", "chinese.dwt"),
                    // 也可以尝试使用现有的打印图模板作为基础
                    Path.Combine(assemblyDir, "Template", "Printeddiagram.dwg")
                };

                foreach (string path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        return path;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                WriteMessage($"\n获取中文模板路径时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 为数据库设置中文支持
        /// </summary>
        private void SetupChineseSupport(Database db)
        {
            try
            {
                using (Transaction trans = db.TransactionManager.StartTransaction())
                {
                    // 设置系统变量以支持中文
                    db.Dimtxsty = ObjectId.Null; // 重置标注文字样式

                    // 创建或更新宋体文字样式
                    TextStyleTable textStyleTable = trans.GetObject(db.TextStyleTableId, OpenMode.ForWrite) as TextStyleTable;
                    if (textStyleTable != null)
                    {
                        string simSunStyleName = "宋体";
                        if (!textStyleTable.Has(simSunStyleName))
                        {
                            TextStyleTableRecord simSunStyle = new TextStyleTableRecord();
                            simSunStyle.Name = simSunStyleName;
                            simSunStyle.FileName = "simsun.ttf"; // 宋体字体文件
                            simSunStyle.Font = new Autodesk.AutoCAD.GraphicsInterface.FontDescriptor("宋体", false, false, 0, 0);
                            textStyleTable.Add(simSunStyle);
                            trans.AddNewlyCreatedDBObject(simSunStyle, true);
                        }

                        // 设置标准文字样式为宋体
                        if (textStyleTable.Has("Standard"))
                        {
                            TextStyleTableRecord standardStyle = trans.GetObject(textStyleTable["Standard"], OpenMode.ForWrite) as TextStyleTableRecord;
                            if (standardStyle != null)
                            {
                                standardStyle.FileName = "simsun.ttf";
                                standardStyle.Font = new Autodesk.AutoCAD.GraphicsInterface.FontDescriptor("宋体", false, false, 0, 0);
                            }
                        }
                    }

                    trans.Commit();
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n设置中文支持时发生错误: {ex.Message}");
            }
        }


        /// <summary>
        /// 在新数据库中重新扫描实体，以获取包含有效ObjectId的实体数据列表。
        /// XData会随实体一同被克隆，所以我们可以再次读取它。
        /// </summary>
        private List<EntityData> ReScanEntitiesInNewDb(Database db, Transaction trans)
        {
            var newEntities = new List<EntityData>();
            try
            {
                BlockTable bt = trans.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                foreach (ObjectId objId in btr)
                {
                    Entity entity = trans.GetObject(objId, OpenMode.ForRead) as Entity;
                    if (entity == null) continue;

                    // 调用现有的XData提取逻辑，它会返回包含新ObjectId的EntityData
                    var entityData = ExtractXDataFromEntity(entity);
                    if (entityData != null)
                    {
                        newEntities.Add(entityData);
                    }
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"\n在新数据库中重新扫描实体时发生错误: {ex.Message}");
            }
            return newEntities;
        }

        /// <summary>
        /// 将DWG文件转换为WMF格式的方法
        /// </summary>
        private void ConvertDwgToWmf(List<string> dwgFiles, string outputDir, Editor ed)
        {
            ed.WriteMessage($"\n开始转换 {dwgFiles.Count} 个DWG文件到WMF格式...");

            try
            {
                string toolPath = Path.Combine(Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "tool", "Acme CAD Converter", "AcmeCADConverter.exe");

                if (!File.Exists(toolPath))
                {
                    ed.WriteMessage($"\n错误: 未找到AcmeCADConverter.exe: {toolPath}");
                    return;
                }

                if (dwgFiles.Count == 0)
                {
                    // ed.WriteMessage("\nNo DWG files to convert.");
                    return;
                }

                // 创建命令行参数，使用固定尺寸
                int dpi = 300;
                string baseArguments = $"/r /e /p 1 /l convert.log /f 3 /d \"{outputDir}\" /w {210}mm /h {297}mm /res {dpi} /key 15KJNKUPIY5D7WL9NSLSYZHCG3SGWMBOX2JYDWBEFPX8A9ZC7GJXMW09MOMCS";

                // 添加所有DWG文件到一个命令中
                string arguments = baseArguments;
                foreach (string dwgFile in dwgFiles)
                {
                    arguments += $" \"{dwgFile}\"";
                }

                // 显示命令行
                // ed.WriteMessage($"\nConverting {dwgFiles.Count} DWG files to WMF...");
                // ed.WriteMessage($"\nCommand: {toolPath} {arguments}");

                // 启动转换进程
                ProcessStartInfo psi = new ProcessStartInfo(toolPath, arguments);
                psi.CreateNoWindow = true;
                psi.UseShellExecute = false;
                psi.RedirectStandardOutput = true;
                psi.RedirectStandardError = true;

                using (Process process = Process.Start(psi))
                {
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        ed.WriteMessage($"\n成功转换 {dwgFiles.Count} 个DWG文件到WMF");
                    }
                    else
                    {
                        ed.WriteMessage($"\n转换DWG文件到WMF失败。错误代码: {process.ExitCode}");
                        if (!string.IsNullOrEmpty(error))
                            ed.WriteMessage($"\n错误: {error}");
                    }
                }
            }
            catch (Exception ex)
            {
                ed.WriteMessage($"\nWMF转换过程错误: {ex.Message}");
            }

            // ed.WriteMessage("\nDWG to WMF conversion complete.");
        }
    }

    /// <summary>
    /// 模板数据类
    /// </summary>
    public class TemplateData
    {
        public Polyline? RedPolyline { get; set; }
        public List<Entity> NorthArrowEntities { get; set; } = new List<Entity>();
        public List<Entity> OtherEntities { get; set; } = new List<Entity>();
    }

    /// <summary>
    /// 实体数据类
    /// </summary>
    public class EntityData
    {
        public ObjectId EntityId { get; set; }
        public string EntityType { get; set; } = "";
        public string XDataAppName { get; set; } = "";
        public string BuildingNumber { get; set; } = "";
        public string UnitNumber { get; set; } = "";
        public string FloorNumber { get; set; } = "";
        public string RoomNumber { get; set; } = "";
        public double NorthArrowOrientation { get; set; } = 0;
    }

}